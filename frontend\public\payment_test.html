<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付宝支付测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .test-section h3 {
            color: #0066cc;
            margin-top: 0;
        }
        .button {
            background-color: #0066cc;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #0052a3;
        }
        .success {
            background-color: #4CAF50;
        }
        .success:hover {
            background-color: #45a049;
        }
        .info {
            background-color: #f0f8ff;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
        }
        .code {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        #paymentResult {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>支付宝沙箱支付测试</h1>
        
        <div class="info">
            <p><strong>当前状态：</strong> 支付宝沙箱已正确配置，使用RSA2签名算法</p>
            <p><strong>网关地址：</strong> https://openapi-sandbox.dl.alipaydev.com/gateway.do</p>
            <p><strong>APP_ID：</strong> 2021000148685433</p>
        </div>

        <div class="test-section">
            <h3>1. 测试后端生成的支付链接</h3>
            <p>点击下面的按钮，通过后端API生成正确的支付宝支付链接：</p>
            <button class="button success" onclick="generatePaymentLink()">生成支付链接</button>
            <div id="paymentResult"></div>
        </div>

        <div class="test-section">
            <h3>2. 前端应用测试</h3>
            <p>通过完整的前端应用测试支付流程：</p>
            <button class="button" onclick="window.open('http://localhost:5173', '_blank')">打开前端应用</button>
            <div class="info">
                <p><strong>测试步骤：</strong></p>
                <ol>
                    <li>登录系统（用户名：testuser，密码：password123）</li>
                    <li>浏览产品并添加到购物车</li>
                    <li>创建订单</li>
                    <li>点击支付按钮</li>
                    <li>使用沙箱账户完成支付</li>
                </ol>
            </div>
        </div>

        <div class="warning">
            <p><strong>沙箱测试账户信息：</strong></p>
            <div class="code">
买家账号: <EMAIL>
登录密码: 111111
支付密码: 111111
            </div>
        </div>

        <div class="test-section">
            <h3>3. 技术说明</h3>
            <p><strong>已修复的问题：</strong></p>
            <ul>
                <li>✅ 统一使用RSA2签名算法</li>
                <li>✅ 修复私钥格式（PKCS1格式，包含PEM头部）</li>
                <li>✅ 统一APP_ID配置</li>
                <li>✅ 使用最新的沙箱网关地址</li>
            </ul>
            
            <p><strong>当前配置：</strong></p>
            <div class="code">
ALIPAY_APP_ID: "2021000148685433"
ALIPAY_PRIVATE_KEY_PATH: "/app/keys/app_private_key.pem"
ALIPAY_PUBLIC_KEY_PATH: "/app/keys/alipay_public_key.pem"
sign_type: "RSA2"
gateway: "https://openapi-sandbox.dl.alipaydev.com/gateway.do"
            </div>
        </div>
    </div>

    <script>
        async function generatePaymentLink() {
            const resultDiv = document.getElementById('paymentResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<p>正在生成支付链接...</p>';
            resultDiv.style.backgroundColor = '#f0f8ff';
            
            try {
                // 这里应该调用后端API生成支付链接
                // 由于这是静态页面，我们显示说明信息
                resultDiv.innerHTML = `
                    <h4>支付链接生成说明</h4>
                    <p>由于这是静态页面，无法直接调用后端API。请通过以下方式测试：</p>
                    <ol>
                        <li><strong>通过前端应用：</strong> 访问 <a href="http://localhost:5173" target="_blank">http://localhost:5173</a></li>
                        <li><strong>直接API调用：</strong> 使用Postman或curl调用支付API</li>
                        <li><strong>Docker命令测试：</strong> 使用上面显示的Docker命令生成测试链接</li>
                    </ol>
                    <p><strong>示例API调用：</strong></p>
                    <div class="code">
POST http://localhost:8000/api/v1/payments/create
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN

{
    "order_id": 1
}
                    </div>
                `;
                resultDiv.style.backgroundColor = '#d4edda';
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">错误: ${error.message}</p>`;
                resultDiv.style.backgroundColor = '#f8d7da';
            }
        }
    </script>
</body>
</html>
