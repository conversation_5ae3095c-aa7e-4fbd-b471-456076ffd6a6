# 支付宝配置参考文档

## 📋 **统一配置标准**

为了避免不同文档中配置不一致的问题，本文档定义了支付宝集成的统一配置标准。

### 🔧 **当前生效配置**

#### 1. **沙箱环境配置**
```yaml
# Docker环境变量 (docker-compose.yml)
ALIPAY_APP_ID: "2021000148685433"
ALIPAY_PRIVATE_KEY_PATH: "/app/keys/app_private_key.pem"
ALIPAY_PUBLIC_KEY_PATH: "/app/keys/alipay_public_key.pem"
ALIPAY_NOTIFY_URL: "http://localhost:8000/api/v1/payments/notify"
ALIPAY_RETURN_URL: "http://localhost:5173/payment-result"
```

#### 2. **网关地址**
```
沙箱环境: https://openapi-sandbox.dl.alipaydev.com/gateway.do
生产环境: https://openapi.alipay.com/gateway.do
```

#### 3. **签名算法**
```
sign_type: RSA2
```

#### 4. **密钥格式**
```
私钥格式: PKCS1 (-----BEGIN RSA PRIVATE KEY-----)
公钥格式: X.509 (-----BEGIN PUBLIC KEY-----)
```

### ⚠️ **重要说明**

1. **APP_ID**: 使用 `2021000148685433` (沙箱应用ID)
2. **私钥文件**: 使用 `app_private_key.pem` (PKCS1格式，无需转换为PKCS8)
3. **网关地址**: 使用最新的沙箱地址 `openapi-sandbox.dl.alipaydev.com`
4. **签名算法**: 必须使用 `RSA2`，不支持 `RSA1`

### 🔍 **常见错误及解决方案**

#### 1. **invalid-signature-type-said-interface**
- **原因**: 使用了不支持的签名算法 (如RSA1)
- **解决**: 确保 `sign_type` 设置为 `RSA2`
- **状态**: ✅ 已修复

#### 2. **invalid-signature (验签出错，sign值与sign_type参数指定的签名类型不一致)**
- **原因**: 虽然设置了RSA2，但实际签名使用了错误的算法或格式
- **解决**: 使用支付宝SDK生成正确的RSA2签名，不要手动构造签名
- **状态**: ✅ 已修复

#### 3. **RSA key format is not supported**
- **原因**: 私钥文件缺少PEM格式头部
- **解决**: 确保私钥文件包含 `-----BEGIN RSA PRIVATE KEY-----` 头部
- **状态**: ✅ 已修复

#### 4. **APP_ID不匹配**
- **原因**: 不同文档中使用了不同的APP_ID
- **解决**: 统一使用 `2021000148685433`
- **状态**: ✅ 已修复

#### 5. **前端测试页面签名错误**
- **原因**: 前端无法生成有效的RSA2签名
- **解决**: 通过后端API生成支付链接，不要在前端直接构造
- **状态**: ✅ 已修复

### 📝 **测试账户信息**

```
买家账号: <EMAIL>
登录密码: 111111
支付密码: 111111
```

### 🎯 **配置验证清单**

- [ ] APP_ID: `2021000148685433`
- [ ] 私钥路径: `/app/keys/app_private_key.pem`
- [ ] 公钥路径: `/app/keys/alipay_public_key.pem`
- [ ] 签名算法: `RSA2`
- [ ] 网关地址: `https://openapi-sandbox.dl.alipaydev.com/gateway.do`
- [ ] 私钥格式: PKCS1 (包含PEM头部)
- [ ] 公钥格式: X.509 (包含PEM头部)

### 🧪 **正确的测试方法**

#### 1. **通过前端应用测试**
```
1. 访问 http://localhost:5173
2. 登录系统（testuser / password123）
3. 浏览产品并创建订单
4. 点击支付按钮
5. 使用沙箱账户完成支付
```

#### 2. **通过API直接测试**
```bash
# 生成测试支付链接
docker exec sales_platform_backend python -c "
from app.services.payment import PaymentService
import time
ps = PaymentService()
order_string = ps.alipay_client.api_alipay_trade_page_pay(
    out_trade_no='TEST_ORDER_' + str(int(time.time())),
    total_amount='0.01',
    subject='测试商品',
    return_url='http://localhost:5173/payment-result',
    notify_url='http://localhost:8000/api/v1/payments/notify'
)
print(f'https://openapi-sandbox.dl.alipaydev.com/gateway.do?{order_string}')
"
```

#### 3. **使用测试页面**
- 访问 `http://localhost:5173/payment_test.html` 查看测试说明
- 不要使用 `alipay_test.html`，因为前端无法生成有效签名

### 📚 **相关文档**

- [支付宝沙箱说明](./sandbox.md)
- [支付宝集成指南](./alipay_integration_guide_combined.md)
- [支付测试页面](../frontend/public/payment_test.html)

---

**最后更新**: 2024年12月
**维护者**: 开发团队
