from typing import Annotated, List

from fastapi import APIRouter, Depends, HTTPException, status

from app.core.dependencies import DB, get_current_active_user, get_current_admin_user
from app.models.user import User
from app.schemas.user import User as UserSchema
from app.schemas.user import UserCreate, UserUpdate
from app.services.user import user_service

# 创建路由器
router = APIRouter(prefix="/users", tags=["users"])


@router.post("/", response_model=UserSchema)
async def create_user(user_create: UserCreate, db: DB):
    """
    创建用户

    Args:
        user_create: 用户创建模型
        db: 数据库会话

    Returns:
        User: 创建的用户对象

    Raises:
        HTTPException: 用户名或邮箱已存在时抛出
    """
    # 检查用户名是否已存在
    db_user = await user_service.get_user_by_username(db, user_create.username)
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在",
        )

    # 检查邮箱是否已存在
    db_user = await user_service.get_user_by_email(db, user_create.email)
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在",
        )

    # 创建用户
    user = await user_service.create_user(db, user_create)
    return user


@router.get("/me", response_model=UserSchema)
async def read_users_me(
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    获取当前用户信息

    Args:
        current_user: 当前用户

    Returns:
        User: 当前用户对象
    """
    return current_user


@router.put("/me", response_model=UserSchema)
async def update_user_me(
    user_update: UserUpdate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: DB,
):
    """
    更新当前用户信息

    Args:
        user_update: 用户更新模型
        current_user: 当前用户
        db: 数据库会话

    Returns:
        User: 更新后的用户对象

    Raises:
        HTTPException: 用户名或邮箱已存在时抛出
    """
    # 检查用户名是否已存在
    if user_update.username and user_update.username != current_user.username:
        db_user = await user_service.get_user_by_username(db, user_update.username)
        if db_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在",
            )

    # 检查邮箱是否已存在
    if user_update.email and user_update.email != current_user.email:
        db_user = await user_service.get_user_by_email(db, user_update.email)
        if db_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在",
            )

    # 更新用户
    user = await user_service.update_user(db, current_user.id, user_update)
    return user


@router.get("/", response_model=List[UserSchema])
async def read_users(
    current_user: Annotated[User, Depends(get_current_admin_user)],
    db: DB,
    skip: int = 0,
    limit: int = 100,
):
    """
    获取用户列表（仅管理员）

    Args:
        skip: 跳过记录数
        limit: 限制记录数
        current_user: 当前管理员用户
        db: 数据库会话

    Returns:
        List[User]: 用户列表
    """
    users = await user_service.get_users(db, skip=skip, limit=limit)
    return users