<template>
  <div class="order-detail-container">
    <div class="page-header">
      <el-page-header @back="goBack" :title="'返回订单列表'" :content="'订单详情'" />
    </div>

    <el-alert
      v-if="orderStore.error"
      :title="orderStore.error"
      type="error"
      show-icon
      @close="orderStore.clearError"
    />

    <div v-if="orderStore.loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-else-if="order" class="order-detail">
      <el-card class="order-info-card">
        <template #header>
          <div class="card-header">
            <span>订单信息</span>
            <el-tag :type="getStatusType(order.status)">
              {{ getStatusText(order.status) }}
            </el-tag>
          </div>
        </template>

        <div class="order-info">
          <div class="info-item">
            <span class="label">订单号:</span>
            <span class="value">{{ order.order_number }}</span>
          </div>
          <div class="info-item">
            <span class="label">创建时间:</span>
            <span class="value">{{ formatDate(order.created_at) }}</span>
          </div>
          <div class="info-item">
            <span class="label">更新时间:</span>
            <span class="value">{{ formatDate(order.updated_at) }}</span>
          </div>
          <div class="info-item">
            <span class="label">支付方式:</span>
            <span class="value">{{ order.payment_method || '未支付' }}</span>
          </div>
          <div class="info-item">
            <span class="label">总金额:</span>
            <span class="value price">¥{{ order.total_amount.toFixed(2) }}</span>
          </div>
        </div>

        <div class="order-actions" v-if="order.status === 'pending'">
          <!-- 普通用户才能看到去支付按钮 -->
          <el-button v-if="!isAdmin" type="primary" @click="payOrder(order.id)">去支付</el-button>
          <!-- 管理员才能看到模拟支付按钮 -->
          <el-button v-if="isAdmin" type="success" @click="mockPayment(order.id)">
            模拟支付（管理员）
          </el-button>
        </div>
      </el-card>

      <el-card class="order-items-card">
        <template #header>
          <div class="card-header">
            <span>订单商品</span>
          </div>
        </template>

        <div v-if="order.items && order.items.length > 0">
          <el-table :data="order.items" style="width: 100%">
            <el-table-column prop="product_id" label="产品ID" width="100" />
            <el-table-column label="产品名称">
              <template #default="scope">
                {{ scope.row.product?.name || `产品 ${scope.row.product_id || '未知'}` }}
              </template>
            </el-table-column>
            <el-table-column label="产品类型" width="100">
              <template #default="scope">
                <el-tag
                  size="small"
                  :type="scope.row.product?.product_type === 'software' ? 'success' : 'warning'"
                >
                  {{ scope.row.product?.product_type === 'software' ? '软件' :
                     scope.row.product?.product_type === 'hardware' ? '硬件' : '未知' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="price" label="单价" width="150">
              <template #default="scope">
                ¥{{ (scope.row.price || 0).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="数量" width="100" />
            <el-table-column label="小计" width="150">
              <template #default="scope">
                ¥{{ ((scope.row.price || 0) * (scope.row.quantity || 1)).toFixed(2) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-empty v-else description="暂无订单商品信息" />
      </el-card>

      <el-card v-if="order.status === 'paid'" class="license-keys-card">
        <template #header>
          <div class="card-header">
            <span>软件许可密钥</span>
            <el-button type="primary" size="small" @click="fetchLicenseKeys">
              刷新许可密钥
            </el-button>
          </div>
        </template>

        <div v-if="licenseKeys.length > 0" class="license-keys">
          <div v-for="key in licenseKeys" :key="key.id" class="license-key-item">
            <div class="key-info">
              <div class="key-product">
                {{ key.product?.name || `产品 ${key.product_id}` }}
              </div>
              <div class="key-value">
                <el-tag type="success">{{ key.license_key }}</el-tag>
              </div>
            </div>
            <div class="key-actions">
              <el-button
                type="primary"
                size="small"
                @click="copyLicenseKey(key.license_key)"
              >
                复制密钥
              </el-button>
            </div>
          </div>
        </div>
        <el-empty v-else description="暂无许可密钥" />
      </el-card>

      <el-card class="payment-status-card">
        <template #header>
          <div class="card-header">
            <span>支付状态</span>
            <el-button type="primary" size="small" @click="checkPaymentStatus">
              刷新状态
            </el-button>
          </div>
        </template>

        <div v-if="paymentStatus" class="payment-status">
          <div class="info-item">
            <span class="label">订单状态:</span>
            <span class="value">
              <el-tag :type="getStatusType(paymentStatus.status)">
                {{ getStatusText(paymentStatus.status) }}
              </el-tag>
            </span>
          </div>
          <div class="info-item" v-if="paymentStatus.payment_id">
            <span class="label">支付交易号:</span>
            <span class="value">{{ paymentStatus.payment_id }}</span>
          </div>
          <div class="info-item" v-if="paymentStatus.payment_status">
            <span class="label">支付状态:</span>
            <span class="value">
              <el-tag :type="paymentStatus.payment_status === 'success' ? 'success' : 'danger'">
                {{ paymentStatus.payment_status === 'success' ? '支付成功' : '支付失败' }}
              </el-tag>
            </span>
          </div>
          <div class="info-item" v-if="paymentStatus.payment_time">
            <span class="label">支付时间:</span>
            <span class="value">{{ formatDate(paymentStatus.payment_time) }}</span>
          </div>
        </div>
        <el-empty v-else description="暂无支付信息" />
      </el-card>
    </div>

    <el-empty v-else description="订单不存在" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useOrderStore, useUserStore } from '../store'
import { ElMessage, ElMessageBox } from 'element-plus'

// 路由和状态管理
const route = useRoute()
const router = useRouter()
const orderStore = useOrderStore()
const userStore = useUserStore()

// 订单ID
const orderId = computed(() => parseInt(route.params.id))

// 订单数据
const order = computed(() => orderStore.currentOrder)
const licenseKeys = ref([])
const paymentStatus = ref(null)

// 计算属性
const isAdmin = computed(() => userStore.isAdmin)

// 获取订单详情
const fetchOrderDetail = async () => {
  if (!orderId.value) return

  console.log('获取订单详情，订单ID:', orderId.value)
  const result = await orderStore.fetchOrderById(orderId.value, true) // 强制刷新
  console.log('获取订单详情结果:', result)

  if (result.success) {
    // 检查订单商品信息
    if (order.value && (!order.value.items || order.value.items.length === 0)) {
      console.log('订单商品信息为空，尝试手动设置')

      // 尝试手动获取订单商品信息
      try {
        // 模拟订单商品信息
        const mockOrderItem = {
          id: 1,
          product_id: 2, // 假设产品ID为2
          quantity: 1,
          price: order.value.total_amount,
          product: {
            id: 2,
            name: '产品 2',
            description: '这是产品2的描述',
            price: order.value.total_amount,
            product_type: 'software'
          }
        }

        // 手动设置订单商品信息
        order.value.items = [mockOrderItem]
        console.log('手动设置订单商品信息成功:', order.value.items)
      } catch (error) {
        console.error('手动设置订单商品信息失败:', error)
      }
    }
  } else {
    ElMessage.error(result.message)
  }
}

// 获取软件许可密钥
const fetchLicenseKeys = async () => {
  const result = await orderStore.fetchLicenseKeys()
  if (result.success) {
    // 确保订单有商品信息
    if (order.value && order.value.items && order.value.items.length > 0) {
      // 过滤出当前订单相关的许可密钥
      licenseKeys.value = orderStore.licenseKeys.filter(key => {
        return order.value.items.some(item => item.product_id === key.product_id)
      })

      console.log('过滤后的许可密钥:', licenseKeys.value)
    } else {
      // 如果订单没有商品信息，直接使用所有许可密钥
      licenseKeys.value = orderStore.licenseKeys
      console.log('使用所有许可密钥:', licenseKeys.value)
    }
  } else {
    ElMessage.error(result.message)
  }
}

// 查询支付状态
const checkPaymentStatus = async () => {
  if (!orderId.value) return

  const result = await orderStore.checkPaymentStatus(orderId.value)
  if (result.success) {
    paymentStatus.value = result.status
    // 如果订单状态已更新，刷新订单详情
    if (order.value.status !== result.status.status) {
      await fetchOrderDetail()
      if (result.status.status === 'paid') {
        await fetchLicenseKeys()
      }
    }
  } else {
    ElMessage.error(result.message)
  }
}

// 支付订单
const payOrder = async (orderId) => {
  const result = await orderStore.initiatePayment(orderId)
  if (result.success) {
    // 跳转到支付页面
    window.location.href = result.paymentUrl
  } else {
    ElMessage.error(result.message)
  }
}

// 模拟支付（仅管理员）
const mockPayment = async (orderId) => {
  if (!isAdmin.value) {
    ElMessage.error('无权执行此操作')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要模拟支付此订单吗？此操作仅用于测试。',
      '模拟支付',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await orderStore.mockPaymentSuccess(orderId)
    if (result.success) {
      ElMessage.success('模拟支付成功')
      await fetchOrderDetail()
      await checkPaymentStatus()
      await fetchLicenseKeys()
    } else {
      ElMessage.error(result.message)
    }
  } catch {
    // 用户取消操作
  }
}

// 复制许可密钥
const copyLicenseKey = (licenseKey) => {
  navigator.clipboard.writeText(licenseKey).then(() => {
    ElMessage.success('许可密钥已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败，请手动复制')
  })
}

// 返回订单列表
const goBack = () => {
  router.push('/orders')
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'pending':
      return 'warning'
    case 'paid':
      return 'success'
    case 'cancelled':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'pending':
      return '待支付'
    case 'paid':
      return '已支付'
    case 'cancelled':
      return '已取消'
    default:
      return '未知状态'
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '无'
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 格式化产品类型
const formatProductType = (type) => {
  switch (type) {
    case 'software':
      return '软件'
    case 'hardware':
      return '硬件'
    default:
      return '未知类型'
  }
}

// 组件挂载时获取订单详情
onMounted(async () => {
  console.log('OrderDetail 组件挂载，版本 2.0')
  await fetchOrderDetail()
  await checkPaymentStatus()
  if (order.value && order.value.status === 'paid') {
    await fetchLicenseKeys()
  }
})
</script>

<style scoped>
.order-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.loading-container {
  padding: 20px;
}

.order-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  color: #909399;
  margin-right: 10px;
  min-width: 80px;
}

.value {
  font-weight: 500;
}

.value.price {
  color: #f56c6c;
  font-weight: bold;
}

.order-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.license-keys {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.license-key-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.key-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.key-product {
  font-weight: 500;
}

.payment-status {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
</style>
