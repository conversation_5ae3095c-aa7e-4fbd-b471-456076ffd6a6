<template>
  <PageContainer customClass="payment-result-page">
    <div class="result-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-animation">
          <el-icon class="loading-icon"><Loading /></el-icon>
        </div>
        <h2 class="loading-title">正在查询支付结果</h2>
        <p class="loading-text">请稍候，我们正在确认您的支付状态...</p>
        <el-progress :percentage="loadingProgress" :show-text="false" />
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-result">
        <el-result
          icon="error"
          title="支付结果查询失败"
          :sub-title="error"
        >
          <template #extra>
            <div class="result-actions">
              <AppButton type="primary" @click="checkPaymentStatus" round>
                <el-icon><RefreshRight /></el-icon>
                重试
              </AppButton>
              <AppButton @click="goToOrderList" round>
                <el-icon><List /></el-icon>
                返回订单列表
              </AppButton>
            </div>
          </template>
        </el-result>
      </div>

      <!-- 支付成功 -->
      <div v-else-if="paymentStatus && paymentStatus.status === 'paid'" class="success-result">
        <div class="result-header">
          <div class="result-icon success">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <h1 class="result-title">支付成功</h1>
          <p class="result-subtitle">您的订单已支付成功，感谢您的购买！</p>
        </div>

        <div class="result-content">
          <div class="order-info">
            <div class="info-item">
              <span class="label">订单号</span>
              <span class="value">{{ paymentStatus.order_number }}</span>
            </div>
            <div class="info-item">
              <span class="label">支付金额</span>
              <span class="value price">¥{{ paymentStatus.amount.toFixed(2) }}</span>
            </div>
            <div class="info-item">
              <span class="label">支付时间</span>
              <span class="value">{{ formatDate(paymentStatus.payment_time) }}</span>
            </div>
          </div>

          <!-- 软件许可密钥 -->
          <div v-if="licenseKeys.length > 0" class="license-keys-section">
            <h2 class="section-title">
              <el-icon><Key /></el-icon>
              您的软件许可密钥
            </h2>
            <div class="license-keys">
              <div v-for="key in licenseKeys" :key="key.id" class="license-key-item">
                <div class="key-info">
                  <div class="key-product">{{ key.product?.name || `产品 ${key.product_id}` }}</div>
                  <div class="key-value">
                    <el-input
                      v-model="key.license_key"
                      readonly
                      :show-password="!key.showKey"
                    >
                      <template #append>
                        <el-button @click="key.showKey = !key.showKey">
                          <el-icon v-if="key.showKey"><Hide /></el-icon>
                          <el-icon v-else><View /></el-icon>
                        </el-button>
                        <el-button @click="copyLicenseKey(key.license_key)">
                          <el-icon><CopyDocument /></el-icon>
                        </el-button>
                      </template>
                    </el-input>
                  </div>
                </div>
              </div>
            </div>
            <div class="key-tips">
              <el-alert
                title="请妥善保管您的许可密钥，它是您使用软件的凭证。"
                type="warning"
                show-icon
                :closable="false"
              />
            </div>
          </div>
        </div>

        <div class="result-actions">
          <AppButton type="primary" @click="goToOrderDetail" round>
            <el-icon><Document /></el-icon>
            查看订单详情
          </AppButton>
          <AppButton @click="goToOrderList" round>
            <el-icon><List /></el-icon>
            返回订单列表
          </AppButton>
          <AppButton type="success" @click="goToProducts" round>
            <el-icon><ShoppingCart /></el-icon>
            继续购物
          </AppButton>
        </div>
      </div>

      <!-- 支付处理中 -->
      <div v-else-if="paymentStatus && paymentStatus.status === 'pending'" class="pending-result">
        <div class="result-header">
          <div class="result-icon pending">
            <el-icon><Clock /></el-icon>
          </div>
          <h1 class="result-title">支付处理中</h1>
          <p class="result-subtitle">您的支付正在处理中，请稍后查看订单状态。</p>
        </div>

        <div class="result-content">
          <div class="order-info">
            <div class="info-item">
              <span class="label">订单号</span>
              <span class="value">{{ paymentStatus.order_number }}</span>
            </div>
            <div class="info-item">
              <span class="label">支付金额</span>
              <span class="value price">¥{{ paymentStatus.amount.toFixed(2) }}</span>
            </div>
          </div>
        </div>

        <div class="result-actions">
          <AppButton type="primary" @click="checkPaymentStatus" round>
            <el-icon><RefreshRight /></el-icon>
            刷新状态
          </AppButton>
          <AppButton @click="goToOrderDetail" round>
            <el-icon><Document /></el-icon>
            查看订单详情
          </AppButton>
          <AppButton @click="goToOrderList" round>
            <el-icon><List /></el-icon>
            返回订单列表
          </AppButton>
        </div>
      </div>

      <!-- 支付失败 -->
      <div v-else class="failed-result">
        <div class="result-header">
          <div class="result-icon failed">
            <el-icon><CircleClose /></el-icon>
          </div>
          <h1 class="result-title">支付失败</h1>
          <p class="result-subtitle">您的支付未完成或已取消，请重新尝试。</p>
        </div>

        <div class="result-content">
          <div v-if="paymentStatus" class="order-info">
            <div class="info-item">
              <span class="label">订单号</span>
              <span class="value">{{ paymentStatus.order_number }}</span>
            </div>
            <div class="info-item">
              <span class="label">支付金额</span>
              <span class="value price">¥{{ paymentStatus.amount.toFixed(2) }}</span>
            </div>
          </div>
        </div>

        <div class="result-actions">
          <AppButton type="primary" @click="retryPayment" v-if="orderId" round>
            <el-icon><RefreshLeft /></el-icon>
            重新支付
          </AppButton>
          <AppButton @click="goToOrderDetail" round>
            <el-icon><Document /></el-icon>
            查看订单详情
          </AppButton>
          <AppButton @click="goToOrderList" round>
            <el-icon><List /></el-icon>
            返回订单列表
          </AppButton>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useOrderStore } from '../store'
import { ElMessage } from 'element-plus'
import PageContainer from '../components/layout/PageContainer.vue'
import {
  Loading,
  RefreshRight,
  RefreshLeft,
  List,
  Document,
  CircleCheck,
  CircleClose,
  Clock,
  Key,
  Hide,
  View,
  CopyDocument,
  ShoppingCart
} from '@element-plus/icons-vue'

// 路由和状态管理
const route = useRoute()
const router = useRouter()
const orderStore = useOrderStore()

// 状态变量
const loading = ref(true)
const error = ref(null)
const paymentStatus = ref(null)
const licenseKeys = ref([])
const loadingProgress = ref(0)
const progressInterval = ref(null)

// 从URL参数中获取订单ID
const orderId = computed(() => {
  const id = route.query.orderId || route.query.order_id
  return id ? parseInt(id) : null
})

// 从URL参数中获取状态
const statusFromUrl = computed(() => route.query.status)

// 检查支付状态
const checkPaymentStatus = async () => {
  if (!orderId.value) {
    error.value = '未找到订单ID，无法查询支付状态'
    loading.value = false
    return
  }

  loading.value = true
  error.value = null
  startLoadingAnimation()

  try {
    console.log('查询支付状态，订单ID:', orderId.value)

    // 先获取订单详情，确保有最新的订单状态
    console.log('获取订单详情')
    const orderResult = await orderStore.fetchOrderById(orderId.value, true)
    console.log('订单详情结果:', orderResult)

    if (orderResult.success && orderStore.currentOrder) {
      // 如果订单已支付，直接使用订单信息
      if (orderStore.currentOrder.status === 'paid') {
        console.log('订单已支付，使用订单信息')
        paymentStatus.value = {
          order_id: orderStore.currentOrder.id,
          order_number: orderStore.currentOrder.order_number,
          status: orderStore.currentOrder.status,
          amount: orderStore.currentOrder.total_amount,
          payment_method: orderStore.currentOrder.payment_method,
          payment_time: orderStore.currentOrder.updated_at
        }

        // 获取许可密钥
        console.log('订单已支付，获取许可密钥')
        await fetchLicenseKeys()
      } else {
        // 如果订单未支付，查询支付状态
        const result = await orderStore.checkPaymentStatus(orderId.value)
        console.log('支付状态查询结果:', result)

        if (result.success) {
          paymentStatus.value = result.status

          // 如果订单已支付，获取许可密钥
          if (result.status.status === 'paid') {
            console.log('支付状态显示已支付，获取许可密钥')
            await fetchLicenseKeys()

            // 刷新订单状态
            await orderStore.fetchOrderById(orderId.value, true)
          }
        } else {
          error.value = result.message || '查询支付状态失败'
          console.error('查询支付状态失败:', result.message)
        }
      }

      // 如果URL中有状态参数，覆盖服务器返回的状态
      if (statusFromUrl.value === 'cancelled') {
        console.log('URL中指定了取消状态，覆盖服务器返回的状态')
        paymentStatus.value = { ...paymentStatus.value, status: 'cancelled' }
      }

      // 如果没有金额信息，从订单中获取
      if (!paymentStatus.value.amount && orderStore.currentOrder) {
        console.log('从订单中获取金额信息')
        paymentStatus.value.amount = orderStore.currentOrder.total_amount
      }
    } else {
      error.value = orderResult.message || '获取订单详情失败'
      console.error('获取订单详情失败:', orderResult.message)
    }
  } catch (err) {
    error.value = '查询支付状态时发生错误'
    console.error('查询支付状态错误:', err)
  } finally {
    stopLoadingAnimation()
    loading.value = false
  }
}

// 获取软件许可密钥
const fetchLicenseKeys = async () => {
  try {
    const result = await orderStore.fetchLicenseKeys()
    if (result.success) {
      // 获取订单详情，以便过滤出相关的许可密钥
      await orderStore.fetchOrderById(orderId.value)

      // 过滤出当前订单相关的许可密钥
      if (orderStore.currentOrder && orderStore.currentOrder.items) {
        const filteredKeys = orderStore.licenseKeys.filter(key => {
          return orderStore.currentOrder.items.some(item => item.product_id === key.product_id)
        })

        // 添加显示控制属性
        licenseKeys.value = filteredKeys.map(key => ({
          ...key,
          showKey: false
        }))
      }
    }
  } catch (err) {
    console.error('获取许可密钥失败', err)
  }
}

// 复制许可密钥
const copyLicenseKey = (licenseKey) => {
  navigator.clipboard.writeText(licenseKey).then(() => {
    ElMessage.success('许可密钥已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败，请手动复制')
  })
}

// 重新支付
const retryPayment = async () => {
  if (!orderId.value) return

  loading.value = true
  try {
    const result = await orderStore.initiatePayment(orderId.value)
    if (result.success) {
      window.location.href = result.paymentUrl
    } else {
      error.value = result.message
      loading.value = false
    }
  } catch (err) {
    error.value = '发起支付时发生错误'
    loading.value = false
    console.error(err)
  }
}

// 导航方法
const goToOrderDetail = () => {
  if (orderId.value) {
    router.push(`/orders/${orderId.value}`)
  } else {
    goToOrderList()
  }
}

const goToOrderList = () => {
  router.push('/orders')
}

const goToProducts = () => {
  router.push('/products')
}

// 加载动画
const startLoadingAnimation = () => {
  loadingProgress.value = 0
  progressInterval.value = setInterval(() => {
    if (loadingProgress.value < 90) {
      loadingProgress.value += Math.random() * 10
    }
  }, 500)
}

const stopLoadingAnimation = () => {
  if (progressInterval.value) {
    clearInterval(progressInterval.value)
    progressInterval.value = null
  }
  loadingProgress.value = 100
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 组件挂载时检查支付状态
onMounted(() => {
  checkPaymentStatus()
})

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  stopLoadingAnimation()
})
</script>

<style scoped>
.payment-result-page {
  max-width: var(--container-max-width);
  margin: 0 auto;
}

.result-container {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-8);
  min-height: 500px;
  display: flex;
  flex-direction: column;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-10) 0;
  text-align: center;
}

.loading-animation {
  margin-bottom: var(--spacing-6);
}

.loading-icon {
  font-size: 48px;
  color: var(--primary-color);
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-3);
  color: var(--text-primary);
}

.loading-text {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-6);
}

.el-progress {
  width: 300px;
}

/* 结果头部 */
.result-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.result-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-bottom: var(--spacing-4);
  font-size: 40px;
}

.result-icon.success {
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
}

.result-icon.pending {
  background-color: rgba(var(--warning-color-rgb), 0.1);
  color: var(--warning-color);
}

.result-icon.failed {
  background-color: rgba(var(--danger-color-rgb), 0.1);
  color: var(--danger-color);
}

.result-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-2);
  color: var(--text-primary);
}

.result-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
}

/* 结果内容 */
.result-content {
  margin-bottom: var(--spacing-8);
}

.order-info {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-4);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.info-item .label {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.info-item .value {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.info-item .price {
  color: var(--accent-color);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
}

/* 许可密钥部分 */
.license-keys-section {
  margin-top: var(--spacing-6);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-4);
  color: var(--text-primary);
}

.license-keys {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.license-key-item {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-4);
}

.key-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.key-product {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-2);
}

.key-tips {
  margin-top: var(--spacing-4);
}

/* 结果操作 */
.result-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .result-container {
    padding: var(--spacing-4);
  }

  .order-info {
    grid-template-columns: 1fr;
    padding: var(--spacing-4);
  }

  .result-actions {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .result-actions button {
    width: 100%;
  }

  .el-progress {
    width: 100%;
  }
}
</style>
