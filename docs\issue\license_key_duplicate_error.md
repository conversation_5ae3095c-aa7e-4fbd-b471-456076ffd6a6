# 许可密钥重复插入错误修复

## 🔍 **错误描述**

```
[查询支付状态失败: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "license_keys_temp_pkey" DETAIL: Key (id)=(2) already exists.
```

## 🎯 **问题根源**

### 1. **数据库序列问题**
- 数据库中已存在 `id=1` 和 `id=2` 的许可密钥记录
- 序列 `license_keys_temp_id_seq` 的当前值为 2
- 当尝试插入新记录时，系统尝试使用 `id=2`，导致主键冲突

### 2. **重复处理支付回调**
- 同一个订单的支付成功回调可能被多次处理
- 每次处理都尝试生成许可密钥，导致重复插入

### 3. **数据库迁移遗留问题**
- 表结构显示使用临时序列名称：`license_keys_temp_id_seq`
- 约束名称也是临时的：`license_keys_temp_pkey`
- 这表明之前的数据库迁移没有完全完成

## ✅ **解决方案**

### 1. **修复数据库序列**

```sql
-- 重置序列到正确的值
SELECT setval('license_keys_temp_id_seq', (SELECT COALESCE(MAX(id), 0) + 1 FROM license_keys));
```

### 2. **添加重复检查逻辑**

在所有生成许可密钥的地方添加重复检查：

```python
# 检查是否已经为此订单和产品生成了许可密钥
existing_key = await db.execute(
    select(LicenseKey).where(
        LicenseKey.order_id == order.id,
        LicenseKey.product_id == product.id
    )
)
if existing_key.scalar_one_or_none() is None:
    # 生成许可密钥
    license_key = order_service.generate_license_key()
    license_key_create = LicenseKeyCreate(
        user_id=order.user_id,
        product_id=product.id,
        order_id=order.id,
        license_key=license_key,
    )
    await order_service.create_license_key(db, license_key_create)
else:
    print(f"许可密钥已存在，跳过生成: 订单={order.id}, 产品={product.id}")
```

### 3. **修改的文件**

- `backend/app/services/payment.py` - 在以下方法中添加重复检查：
  - `handle_payment_notification()` - 支付宝回调处理
  - `check_payment_status()` - 查询支付状态
  - `mock_payment_success()` - 模拟支付成功

## 🔧 **技术细节**

### 数据库表结构问题

当前表结构显示：
```sql
id | integer | not null | nextval('license_keys_temp_id_seq'::regclass)
```

约束名称：
```
"license_keys_temp_pkey" PRIMARY KEY, btree (id)
```

这些都是临时迁移过程中的名称，应该在生产环境中规范化。

### 并发处理问题

支付回调可能存在并发处理的情况：
1. 支付宝异步通知
2. 前端查询支付状态
3. 用户手动刷新

这些操作可能同时触发许可密钥生成，导致冲突。

## 🎯 **预防措施**

### 1. **数据库约束**
- 在 `license_keys` 表上添加唯一约束：`(order_id, product_id)`
- 确保同一订单的同一产品只能有一个许可密钥

### 2. **事务处理**
- 使用数据库事务确保操作的原子性
- 在事务失败时正确回滚

### 3. **幂等性设计**
- 所有支付处理操作都应该是幂等的
- 重复调用不应该产生副作用

## 📋 **验证步骤**

1. **检查序列值**：
   ```sql
   SELECT last_value FROM license_keys_temp_id_seq;
   ```

2. **检查现有数据**：
   ```sql
   SELECT id, order_id, product_id FROM license_keys ORDER BY id;
   ```

3. **测试支付流程**：
   - 创建新订单并支付
   - 验证许可密钥是否正确生成
   - 重复支付回调，确认不会重复生成

## 🎉 **修复结果**

- ✅ **序列同步**：数据库序列已重置到正确值
- ✅ **重复检查**：添加了许可密钥重复生成检查
- ✅ **错误处理**：改进了错误处理和日志记录
- ✅ **幂等性**：支付处理现在是幂等的

## 📚 **相关文档**

- [数据库管理文档](../database_management.md)
- [支付集成指南](../alipay_integration_guide_combined.md)
- [许可密钥管理](../license_key_management.md)

---

**修复时间**: 2024年12月
**影响范围**: 支付处理、许可密钥生成
**状态**: 已解决
