<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付宝沙箱测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        h1 {
            color: #0066cc;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0055aa;
        }
        .note {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f8f8;
            border-left: 4px solid #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>支付宝沙箱测试</h1>
        <p>这个页面用于测试支付宝沙箱支付功能，绕过后端直接提交到支付宝网关。</p>

        <form action="https://openapi-sandbox.dl.alipaydev.com/gateway.do" method="GET">
            <!-- 基本参数 -->
            <input type="hidden" name="charset" value="utf-8">
            <input type="hidden" name="method" value="alipay.trade.page.pay">
            <input type="hidden" name="sign_type" value="RSA2">
            <input type="hidden" name="timestamp" id="timestamp">
            <input type="hidden" name="version" value="1.0">

            <!-- 应用参数 -->
            <div class="form-group">
                <label for="app_id">应用ID (APPID)</label>
                <input type="text" id="app_id" name="app_id" value="2088721067958003">
            </div>

            <!-- 业务参数 -->
            <div class="form-group">
                <label for="out_trade_no">商户订单号</label>
                <input type="text" id="out_trade_no" name="out_trade_no" value="TEST_ORDER_123456">
            </div>

            <div class="form-group">
                <label for="total_amount">订单金额</label>
                <input type="text" id="total_amount" name="total_amount" value="0.01">
            </div>

            <div class="form-group">
                <label for="subject">订单标题</label>
                <input type="text" id="subject" name="subject" value="测试商品">
            </div>

            <div class="form-group">
                <label for="product_code">产品码</label>
                <input type="text" id="product_code" name="product_code" value="FAST_INSTANT_TRADE_PAY">
            </div>

            <!-- 回调地址 -->
            <div class="form-group">
                <label for="return_url">同步通知地址</label>
                <input type="text" id="return_url" name="return_url" value="http://localhost:5173/payment-result">
            </div>

            <div class="form-group">
                <label for="notify_url">异步通知地址</label>
                <input type="text" id="notify_url" name="notify_url" value="http://localhost:8000/api/v1/payments/notify">
            </div>

            <!-- 业务扩展参数 -->
            <input type="hidden" name="biz_content" id="biz_content">

            <!-- 签名 -->
            <div class="form-group">
                <label for="sign">签名 (留空，仅用于测试)</label>
                <input type="text" id="sign" name="sign" value="test_sign_value">
            </div>

            <button type="submit">提交到支付宝沙箱</button>
        </form>

        <div class="note">
            <p><strong>注意：</strong> 这个表单仅用于测试支付宝沙箱网关是否可访问。由于没有正确的签名，支付宝会返回签名验证失败的错误，但这可以帮助我们确认网关是否正常工作。</p>
            <p>测试买家账号: <EMAIL></p>
            <p>登录密码: 111111</p>
            <p>支付密码: 111111</p>
        </div>
    </div>

    <script>
        // 设置当前时间戳
        document.getElementById('timestamp').value = new Date().toISOString().replace('T', ' ').substring(0, 19);

        // 设置业务参数
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('form').addEventListener('submit', function(e) {
                const bizContent = {
                    out_trade_no: document.getElementById('out_trade_no').value,
                    total_amount: document.getElementById('total_amount').value,
                    subject: document.getElementById('subject').value,
                    product_code: document.getElementById('product_code').value
                };
                document.getElementById('biz_content').value = JSON.stringify(bizContent);
            });
        });
    </script>
</body>
</html>
