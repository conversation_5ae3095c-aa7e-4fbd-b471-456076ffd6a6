---
description: 
globs: 
alwaysApply: false
---
# London school TDD

## Red-Green-Refactor 循环
- **Red**：先编写一个失败的测试
- **Green**：实现最小代码使测试通过
- **Refactor**：在测试通过的基础上重构优化

## 测试优先原则
- 严格遵循"测试先行，实现后至"
- 基于伪代码中的TDD锚点编写测试
- 每个测试对应一个具体功能点

## 测试隔离原则
- 使用依赖注入和测试替身
- 保持测试之间的独立性
- 避免测试之间的状态共享

## 最小实现原则
- 实现代码应该刚好满足测试需求
- 避免过度设计和预留扩展
- 通过重构阶段优化代码结构

## 验证要点
- 确保每个测试都有明确的失败原因
- 验证测试覆盖了所有关键路径
- 测试应该快速且可重复执行

## 整体开发流程
1. 从伪代码中识别测试锚点
2. 编写失败的测试
3. 实现最小可通过代码
4. 重构优化代码结构
5. 继续下一个测试锚点

这样可以确保代码质量，同时避免过度测试或测试不足的问题。

